package com.fxiaoke.file.server.help;

import com.fxiaoke.file.server.domain.exception.FileServerException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 虚拟线程线程池管理类
 */
@Slf4j
@Component
public class VirtualThreadPoolHelper {

    public static final String MODULE_NAME = "VirtualThreadPoolHelper";

    /**
     * 获取虚拟线程执行器
     */
    @Getter
    private ExecutorService virtualThreadExecutor;

    /**
     * CompletableFuture
     */
    private Executor virtualThreadAsyncExecutor;

    /**
     * 初始化虚拟线程池
     */
    @PostConstruct
    public void init() {
        try {
            // 创建虚拟线程执行器
            this.virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
            this.virtualThreadAsyncExecutor = virtualThreadExecutor;
            log.info("虚拟线程池初始化成功");
        } catch (Exception e) {
            log.error("虚拟线程池初始化失败", e);
            // 降级方案：使用普通线程池
            this.virtualThreadExecutor = Executors.newFixedThreadPool(10);
            this.virtualThreadAsyncExecutor = virtualThreadExecutor;
        }
    }

    /**
     * 销毁线程池
     */
    @PreDestroy
    public void destroy() {
        if (virtualThreadExecutor != null && !virtualThreadExecutor.isShutdown()) {
            virtualThreadExecutor.shutdown();
            try {
                if (!virtualThreadExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    virtualThreadExecutor.shutdownNow();
                }
                log.info("虚拟线程池已关闭");
            } catch (InterruptedException e) {
                virtualThreadExecutor.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("虚拟线程池关闭时被中断", e);
            }
        }
    }

    /**
     * 提交任务并返回Future
     * 
     * @param task 要执行的任务
     * @param <T>  返回值类型
     * @return Future对象
     */
    public <T> Future<T> submitTask(Callable<T> task) {
        try {
            return virtualThreadExecutor.submit(task);
        } catch (Exception e) {
            log.error("提交虚拟线程任务失败", e);
            throw new FileServerException(MODULE_NAME, "提交虚拟线程任务失败", e);
        }
    }

    /**
     * 提交无返回值的任务
     * 
     * @param task 要执行的任务
     * @return Future对象
     */
    public Future<?> submitTask(Runnable task) {
        try {
            return virtualThreadExecutor.submit(task);
        } catch (Exception e) {
            log.error("提交虚拟线程任务失败", e);
            throw new FileServerException(MODULE_NAME, "提交虚拟线程任务失败", e);
        }
    }

    /**
     * 使用CompletableFuture异步执行任务
     * 
     * @param supplier 任务供应商
     * @param <T>      返回值类型
     * @return CompletableFuture对象
     */
    public <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        try {
            return CompletableFuture.supplyAsync(supplier, virtualThreadAsyncExecutor);
        } catch (Exception e) {
            log.error("创建CompletableFuture任务失败", e);
            throw new FileServerException(MODULE_NAME, "创建CompletableFuture任务失败", e);
        }
    }

    /**
     * 使用CompletableFuture异步执行无返回值任务
     * 
     * @param runnable 要执行的任务
     * @return CompletableFuture对象
     */
    public CompletableFuture<Void> runAsync(Runnable runnable) {
        try {
            return CompletableFuture.runAsync(runnable, virtualThreadAsyncExecutor);
        } catch (Exception e) {
            log.error("创建CompletableFuture任务失败", e);
            throw new FileServerException(MODULE_NAME, "创建CompletableFuture任务失败", e);
        }
    }

    /**
     * 直接在虚拟线程中执行任务（不等待结果）
     * 
     * @param task 要执行的任务
     */
    public void executeAsync(Runnable task) {
        try {
            virtualThreadExecutor.execute(task);
        } catch (Exception e) {
            log.error("执行虚拟线程任务失败", e);
            throw new FileServerException(MODULE_NAME, "执行虚拟线程任务失败", e);
        }
    }

    /**
     * 创建一个新的虚拟线程并启动
     * 
     * @param task 要执行的任务
     * @return Thread对象
     */
    public Thread startVirtualThread(Runnable task) {
        try {
            Thread virtualThread = Thread.ofVirtual().start(task);
            log.debug("创建并启动虚拟线程: {}", virtualThread.getName());
            return virtualThread;
        } catch (Exception e) {
            log.error("创建虚拟线程失败", e);
            throw new FileServerException(MODULE_NAME, "创建虚拟线程失败", e);
        }
    }

    /**
     * 创建一个命名的虚拟线程并启动
     * 
     * @param name 线程名称
     * @param task 要执行的任务
     * @return Thread对象
     */
    public Thread startVirtualThread(String name, Runnable task) {
        try {
            Thread virtualThread = Thread.ofVirtual().name(name).start(task);
            log.debug("创建并启动命名虚拟线程: {}", virtualThread.getName());
            return virtualThread;
        } catch (Exception e) {
            log.error("创建命名虚拟线程失败: {}", name, e);
            throw new FileServerException(MODULE_NAME, "创建命名虚拟线程失败", e);
        }
    }

    /**
     * 批量提交任务并等待所有任务完成
     * 
     * @param <T> 返回值类型
     * @return 所有任务的结果列表
     * @throws InterruptedException 如果等待被中断
     * @throws ExecutionException   如果任务执行失败
     */
    public <T> CompletableFuture<Void> allOf(CompletableFuture<T>... futures) {
        return CompletableFuture.allOf(futures);
    }

    /**
     * 批量提交任务，返回第一个完成的任务结果
     * 
     * @param futures CompletableFuture数组
     * @param <T>     返回值类型
     * @return 第一个完成的任务结果
     */
    public <T> CompletableFuture<Object> anyOf(CompletableFuture<T>... futures) {
        return CompletableFuture.anyOf(futures);
    }

    /**
     * 检查虚拟线程池是否可用
     * 
     * @return true如果可用，false如果已关闭
     */
    public boolean isAvailable() {
        return virtualThreadExecutor != null && !virtualThreadExecutor.isShutdown();
    }
}