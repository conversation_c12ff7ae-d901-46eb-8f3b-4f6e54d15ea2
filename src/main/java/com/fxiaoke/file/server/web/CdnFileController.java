package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import com.fxiaoke.file.server.service.CdnFileService;
import com.fxiaoke.stone.commons.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/FilesCdn")
@Slf4j(topic = "FileMetaController")
public class CdnFileController {

  private final CdnFileService cdnFileService;

  public CdnFileController(CdnFileService cdnFileService) {
    this.cdnFileService = cdnFileService;
  }

  @PostMapping("/pathToCdnFile")
  public R<String> pathToCdnFile(@RequestBody PathToCdnFileReq req) {
    String cdnReqPath = cdnFileService.pathToCdnFile(req);
    return R.ok(cdnReqPath);
  }
}
