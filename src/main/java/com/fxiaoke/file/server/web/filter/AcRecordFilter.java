package com.fxiaoke.file.server.web.filter;

import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.filter.OncePerRequestFilter;

@Slf4j(topic = "AcRecordFilter")
public class AcRecordFilter extends OncePerRequestFilter {

  // 预定义常量
  private static final String UNKNOWN = "unknown";
  private static final String X_FORWARDED_FOR = "X-Forwarded-For";
  private static final String X_REAL_IP = "X-Real-IP";
  private static final String SEPARATOR = " | ";
  private static final String COOKIE_PREFIX = "Cookie[";
  private static final String HEADER_PREFIX = "Header[";
  private static final String SUFFIX = "]";
  private static final String EQUALS = "=";
  private static final String IP_PREFIX = "IP: ";
  private static final String N_A = "N/A";

  private final CmsPropertiesConfig config;

  // 运行时集合，提供O(1)查找性能
  private Set<String> cookieNames;
  private Set<String> headerNames;

  public AcRecordFilter(CmsPropertiesConfig cmsPropertiesConfig) {
    this.config = cmsPropertiesConfig;
    initCollections();
  }

  /**
   * 初始化运行时集合
   * 既然Filter能够注册，说明配置已经验证有效，无需重复检查
   */
  private void initCollections() {
    // 初始化Cookie名称集合
    cookieNames = config.getFilterRecordCookies() != null && !config.getFilterRecordCookies().isEmpty()
        ? new HashSet<>(config.getFilterRecordCookies())
        : Collections.emptySet();

    // 初始化Header名称集合
    headerNames = config.getFilterRecordHeaders() != null && !config.getFilterRecordHeaders().isEmpty()
        ? new HashSet<>(config.getFilterRecordHeaders())
        : Collections.emptySet();
  }

  @Override
  protected void doFilterInternal(
      @NotNull HttpServletRequest request,
      @NotNull HttpServletResponse response,
      @NotNull FilterChain filterChain) throws IOException, ServletException {

    // 既然Filter能注册说明已启用且配置有效，只需检查日志级别
    if (log.isInfoEnabled()) {
      logRecord(request);
    }

    // 继续执行过滤器链
    filterChain.doFilter(request, response);
  }


  /**
   * 记录配置的值
   */
  private void logRecord(HttpServletRequest request) {
    StringBuilder sb = new StringBuilder(request.getMethod() + " " + request.getRequestURI() + SEPARATOR);

    // 添加IP信息（如果配置了总是记录IP）
    if (config.isFilterRecordIp()) {
      sb.append(IP_PREFIX)
        .append(getClientIP(request))
        .append(SEPARATOR);
    }

    // 收集配置的Header值
    logRecordHeader(request, sb);

    // 收集配置的Cookie值
    logRecordCookies(request, sb);

    // 记录日志
    log.info("AcRecord: {}", sb);
  }

  /**
   * 收集配置的Header值
   */
  private void logRecordHeader(HttpServletRequest request, StringBuilder sb) {
    // 直接使用预初始化的集合，无需重复检查配置
    for (String headerName : headerNames) {
      String headerValue = request.getHeader(headerName);
      // 只有当Header存在且非空时才添加
      if (headerValue != null && !headerValue.trim().isEmpty()) {
        sb.append(HEADER_PREFIX)
            .append(headerName)
            .append(EQUALS)
            .append(truncateValue(headerValue))
            .append(SUFFIX)
            .append(SEPARATOR);

      }
    }
  }

  /**
   * 收集配置的Cookie值
   */
  private void logRecordCookies(HttpServletRequest request, StringBuilder sb) {
    if (cookieNames.isEmpty()) {
      return;
    }

    Cookie[] cookies = request.getCookies();
    if (cookies == null) {
      return;
    }

    for (Cookie cookie : cookies) {
      if (cookieNames.contains(cookie.getName())) {
        String cookieValue = cookie.getValue();
        // 只有当Cookie值存在且非空时才添加
        if (cookieValue != null && !cookieValue.trim().isEmpty()) {
          sb.append(COOKIE_PREFIX)
              .append(cookie.getName())
              .append(EQUALS)
              .append(truncateValue(cookieValue))
              .append(SUFFIX)
              .append(SEPARATOR);
        }
      }
    }
  }

  /**
   * 截断值防止日志过长
   */
  private String truncateValue(String value) {
    if (value == null) {
      return N_A;
    }

    String trimmedValue = value.trim();
    if (trimmedValue.length() > config.getFilterRecordMaxValueLength()) {
      return trimmedValue.substring(0, config.getFilterRecordMaxValueLength()) + "...";
    }

    return trimmedValue;
  }

  /**
   * 获取客户端真实IP地址
   */
  private String getClientIP(HttpServletRequest request) {
    // 检查X-Forwarded-For
    String xForwardedFor = request.getHeader(X_FORWARDED_FOR);
    if (isValidIP(xForwardedFor)) {
      int commaIndex = xForwardedFor.indexOf(',');
      return commaIndex > 0 ? xForwardedFor.substring(0, commaIndex).trim() : xForwardedFor.trim();
    }

    // 检查X-Real-IP
    String xRealIP = request.getHeader(X_REAL_IP);
    if (isValidIP(xRealIP)) {
      return xRealIP.trim();
    }

    // 返回远程地址
    String remoteAdder = request.getRemoteAddr();
    return remoteAdder != null ? remoteAdder : N_A;
  }

  /**
   * 检查IP是否有效
   */
  private boolean isValidIP(String ip) {
    return ip != null && !ip.isEmpty() && !UNKNOWN.equalsIgnoreCase(ip);
  }
}

