package com.fxiaoke.file.server.web.resolver;

import com.fxiaoke.file.server.domain.model.api.request.SignFileUpRequest;
import com.fxiaoke.file.server.utils.StrUtils;
import jakarta.ws.rs.BadRequestException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Component
public class SignFileUpRequestArgumentResolver implements HandlerMethodArgumentResolver {

  // 定义必需的header字段
  private static final Set<String> REQUIRED_HEADERS = Set.of(
      "acid", "resource", "ak", "sign", "expiry",
      "filename", "size", "digest", "User-Agent"
  );

  // 错误消息模板
  private static final String ERROR_MESSAGE_TEMPLATE = "Missing required headers: %s";

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return SignFileUpRequest.class.equals(parameter.getParameterType());
  }

  @Override
  public Object resolveArgument(@NotNull MethodParameter parameter,
      ModelAndViewContainer mavContainer,
      @NotNull NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory) throws MissingRequestHeaderException {

    // 一次性获取所有headers
    Map<String, String> headers = collectHeaders(webRequest);

    // 验证必需的headers
    validateRequiredHeaders(headers, parameter);

    try {
      // 绑定请求参数
      return buildRequest(headers);
    } catch (NumberFormatException e) {
      throw new BadRequestException("Invalid number format: " + e.getMessage());
    }
  }

  private Map<String, String> collectHeaders(NativeWebRequest webRequest) {
    // 使用HashMap预分配足够容量
    Map<String, String> headers = HashMap.newHashMap(REQUIRED_HEADERS.size());
    for (String headerName : REQUIRED_HEADERS) {
      headers.put(headerName, webRequest.getHeader(headerName));
    }
    return headers;
  }

  private void validateRequiredHeaders(Map<String, String> headers, MethodParameter parameter)
      throws MissingRequestHeaderException {
    List<String> missingHeaders = headers.entrySet().stream()
        .filter(e -> StrUtils.isBlank(e.getValue()))
        .map(Map.Entry::getKey)
        .toList();

    if (!missingHeaders.isEmpty()) {
      throw new MissingRequestHeaderException(
          String.format(ERROR_MESSAGE_TEMPLATE, String.join(", ", missingHeaders)),
          parameter);
    }
  }

  private SignFileUpRequest buildRequest(Map<String, String> headers) {
    SignFileUpRequest request = new SignFileUpRequest();

    // 设置字符串类型的字段
    request.setAcid(headers.get("acid"));
    request.setResource(headers.get("resource"));
    request.setAk(headers.get("ak"));
    request.setSign(headers.get("sign"));
    request.setFileName(headers.get("filename"));
    request.setDigest(headers.get("digest"));
    request.setUserAgent(headers.get("User-Agent"));

    // 设置数值类型的字段
    request.setExpiry(Long.parseLong(headers.get("expiry")));
    request.setSize(Integer.parseInt(headers.get("size")));

    return request;
  }
}

