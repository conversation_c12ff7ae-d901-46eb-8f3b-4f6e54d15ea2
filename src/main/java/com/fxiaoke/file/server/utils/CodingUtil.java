package com.fxiaoke.file.server.utils;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import java.time.Instant;
import java.util.List;

public class CodingUtil {
  private static final String MODULE="CodingUtil";
  private CodingUtil() {
  }

  public static boolean assertSignExpire(Long expires) {
    return expires < Instant.now().toEpochMilli();
  }

  public static void assertSignNotExpire(Long expires,Object... params){
    if (expires < Instant.now().toEpochMilli()) {
      throw new FileServerException(MODULE, ErInfo.AUTH_EXPIRED_SIGN,params);
    }
  }
  public static void assertMessageDigest(String message,String digest){
    if (!SignatureUtil.validateMessageDigest(message,digest)) {
      throw new FileServerException(MODULE,ErInfo.AUTH_MESSAGE_LOSS,message,digest);
    }
  }

  /**
   * 校验访问签名是否一致
   * @param secretKey 访问AK对应的SK
   * @param raw       签名原文
   * @param signature 签名
   */
  public static void assertAcSignConsist(String secretKey,String raw,String signature){
    if (!SignatureUtil.validateSignature(secretKey, raw, signature)) {
      throw new FileServerException(MODULE,ErInfo.AUTH_INVALID_SIGN,raw,signature);
    }
  }

  /**
   * 校验元数据签名是否一致
   * @param secretKey 访问AK对应的SK
   * @param raw       签名原文
   * @param signature 签名
   */
  public static void assertMetaDataSignConsist(String secretKey,String raw,String signature){
    if (!SignatureUtil.validateSignature(secretKey, raw, signature) &&
        //2024-04-01-2024-04-18 号之间元数据存在部分计算签名path包含.jpg后缀,所以这里要做兼容
        (!SignatureUtil.validateSignature(secretKey, raw.concat(".jpg"), signature))){
        throw new FileServerException(MODULE,ErInfo.AUTH_INVALID_META_SIGN,raw,signature);
    }
  }

  public static void assertAcidFormat(List<String> userInfoList) {
    if (userInfoList.size() < 2) {
      throw new FileServerException(MODULE,ErInfo.AUTH_MISS_USER_INFO,userInfoList);
    }
  }

  /**
   * 如果条件为 false，则抛出异常。
   *
   * @param condition 条件
   * @param fieldName 字段的业务名称
   * @param args      传递给异常的上下文对象
   */
  public static void throwIfFalse(boolean condition, String fieldName, Object... args) {
    if (!condition) {
      throw new FileServerException(MODULE, 400, fieldName + " is invalid", args);
    }
  }

  /**
   * 如果对象为 null，则抛出异常。
   *
   * @param object    要检查的对象
   * @param fieldName 字段的业务名称
   * @param args      传递给异常的上下文对象
   */
  public static void throwIfNull(Object object, String fieldName, Object... args) {
    if (object == null) {
      throw new FileServerException(MODULE, 400, fieldName + " cannot be null",args);
    }
  }

  /**
   * 如果字符串为空白，则抛出异常。
   *
   * @param str       要检查的字符串
   * @param fieldName 字段的业务名称
   * @param args      传递给异常的上下文对象
   */
  public static void throwIfBlank(String str, String fieldName, Object... args) {
    if (Strings.isBlank(str)) {
      throw new FileServerException(MODULE, 400, fieldName + " cannot be blank", args);
    }
  }

  /**
   * 校验 Employee ID
   *
   * @param employeeId 要检查的 ID
   * @param args       传递给异常的上下文对象
   */
  public static void throwIfEmployeeIdInvalid(Integer employeeId, Object... args) {
    if (employeeId == null || (employeeId <= 0 && employeeId != -10000)) {
      throw new FileServerException(MODULE, 400,
          "Employee ID must be " + -10000 + " or a positive integer", args);
    }
  }
}
