package com.fxiaoke.file.server.utils;

import com.fxiaoke.common.Guard;
import lombok.SneakyThrows;

/**
 * 验证token
 */
public final class EncryptUtils {
  private static final String key = "F~@$^*)+_(&%#!~S";
  private static final Guard encrypt = new Guard(key);

  private EncryptUtils() {
  }

  @SneakyThrows
  public static String encode(String raw) {
    return encrypt.encode(raw);
  }

  @SneakyThrows
  public static String decode(String token) {
    return encrypt.decode(token);
  }

  public static void main(String[] args) throws Exception {
    System.out.println(decode("C506D15D9C2EC27D7928F9185F348A9ACAA2B2BAA2E5E3B65643315CD8F7A919"));
    System.out.println(decode("538581ABE57F6D22907D34CAA7664E16644628D909B599D22EB9C9C28093ABC493DFED1E46DD212B"));
  }
}