package com.fxiaoke.file.server.domain.constants;

public class Constant {

  private Constant() {
  }

  public static final Integer DEFAULT_EM_ID = -10000;
  public static final Integer DEFAULT_DOWN_STREAM_EM_ID = -9527;
  public static final String BUSINESS = "fs-file-server";
  public static final String DEFAULT_SECURITY_GROUP = "XiaoKeNetDisk";
  public static final String RPC_TRACE_IGNORE_CODE = "s312010003";
  public static final String DEFAULT_CONTENT_ENCODING = "UTF-8";
  public static final String DEFAULT_CACHE_CONTROL = "max-age=31536000";
  public static final String DEFAULT_CONTENT_DISPOSITION = "inline;";

  public static final String NULL = null;

  public static final String N_DB_PREFIX = "DFS_";

  // 默认使用https
  public static final String DEFAULT_HTTP_TYPE = "https";
  public static final String DEFAULT_S3_CONFIG_NAME ="fs-stone-enterprise-cloud-toml";
  public static final String DEFAULT_S3_CLIENT="common";
  public static final String FILE_PATH = "/opt/chunk/";
}

