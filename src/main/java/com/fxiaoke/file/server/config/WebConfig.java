package com.fxiaoke.file.server.config;

import com.fxiaoke.file.server.web.resolver.HeaderBasedArgumentResolver;
import com.fxiaoke.file.server.config.condition.AcRecordFilterCondition;
import com.fxiaoke.file.server.web.filter.AcRecordFilter;
import com.fxiaoke.file.server.web.resolver.SignFileUpRequestArgumentResolver;
import com.github.filter.CoreFilter;
import jakarta.servlet.MultipartConfigElement;
import java.util.List;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

  private final SignFileUpRequestArgumentResolver signFileUpRequestArgumentResolver;
  private final HeaderBasedArgumentResolver headerBasedArgumentResolver;

  public WebConfig(SignFileUpRequestArgumentResolver signFileUpRequestArgumentResolver,
      HeaderBasedArgumentResolver headerBasedArgumentResolver) {
    this.signFileUpRequestArgumentResolver = signFileUpRequestArgumentResolver;
    this.headerBasedArgumentResolver = headerBasedArgumentResolver;
  }

  // 配置跨域
  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/FilesOne/**")
        .allowedOrigins("*")
        .allowedMethods("GET", "POST", "OPTIONS")
        .allowedHeaders("*")
        .allowCredentials(false)
        .maxAge(3600);
  }

  @Bean
  public FilterRegistrationBean<CoreFilter> coreFilter(){
    CoreFilter coreFilter=new CoreFilter();
    FilterRegistrationBean<CoreFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(coreFilter);
    registrationBean.setOrder(0);
    registrationBean.addUrlPatterns("/FilesOne/*","/FilesMeta/*");
    return registrationBean;
  }

  @Bean
  @Conditional(AcRecordFilterCondition.class)
  public FilterRegistrationBean<AcRecordFilter> acRecordFilterRegistration(CmsPropertiesConfig cmsPropertiesConfig) {
    FilterRegistrationBean<AcRecordFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(new AcRecordFilter(cmsPropertiesConfig));

    // 将配置的路径转换为servlet URL pattern格式
    // 此时已确保filterRecordPaths不为空
    List<String> filterPaths = cmsPropertiesConfig.getFilterRecordPaths();
    String[] urlPatterns = filterPaths.toArray(String[]::new);
    registrationBean.addUrlPatterns(urlPatterns);

    registrationBean.setName("acRecordFilter");
    registrationBean.setOrder(1);
    return registrationBean;
  }

  @Bean
  public MultipartConfigElement multipartConfigElement() {
    MultipartConfigFactory factory = new MultipartConfigFactory();
    // 单个文件最大
    factory.setMaxFileSize(DataSize.ofMegabytes(100));
    // 设置总上传数据总大小
    factory.setMaxRequestSize(DataSize.ofMegabytes(100));
    return factory.createMultipartConfig();
  }

  // 注册自定义参数解析器
  @Override
  public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
    resolvers.add(signFileUpRequestArgumentResolver);
    resolvers.add(headerBasedArgumentResolver);
  }

}
