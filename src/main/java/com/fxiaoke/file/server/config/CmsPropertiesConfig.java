package com.fxiaoke.file.server.config;

import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@Slf4j(topic = "CmsPropertiesConfig")
@ConfigurationProperties(prefix = "cms.file.server")
public class CmsPropertiesConfig {
  // ZK 地址
  private String zookeeper;
  // 头像文件元数据MongoDB配置名
  private String avatarMongoConfigName;
  // 头像文件FastDFS存储配置名
  private String avatarFastDFSConfigName;
  // 头像文件元数据在Service层JVM缓存条数
  private int avatarFileMetaCacheSize;

  private String s3ConfigName;
  private String s3EncryKey;

  // configName:fs-warehouse-enterprise-metadata-mongo
  private String warehouseMongoConfigName;

  // configName:fs-stone-metadata-mongo-n (仅测试环境使用)
  private String nMongoConfigName;

  // configName:fs-stone-metadata-mongo-n1
  private String n1MongoConfigName;
  // configName:fs-stone-metadata-mongo-n2
  private String n2MongoConfigName;

  // configName:fs-stone-shard-mongo
  private String shardMongoConfigName;

  private String stoneRedisConfigName;

  private String atsHttpClientConfigName;
  private String fsiProxyConfigName;

  private String crossCloudHttpClientConfigName;
  private String fsImageServerHost;

  private List<String> atsServerNodes;

  private String atsServerNodeHost;

  private String atsRequestTemplate;
  private String atsThumbRequestTemplate;
  private boolean atsCacheEnable;
  // 图片处理服务节点
  private List<String> imaginaryServerNodes;
  private int imageSizeThreshold;

  private String em6DefaultAppid;

  // 0*0 原图查看支持的最大图片大小 (超出此值进入分辨率压缩流程)
  private int maxImageSize= 1048576;
  // 0*0 原图查看支持的最大不压缩分辨率（超出此值强制压缩分辨率）
  // Full HD 1920*1080 = 2073600 QHD 2560*1440 = 3686400
  private int maxCompressRes = 2073600;

  /**
   * 是否启用过滤器
   */
  private boolean enabledFilterLogRecord;

  /**
   * 白名单路径模式 - 只有匹配的路径才会被处理
   */
  private List<String> filterRecordPaths;

  /**
   * 是否总是记录IP地址
   */
  private boolean filterRecordIp;

  /**
   * 需要记录的Cookie名称列表
   */
  private List<String> filterRecordCookies;

  /**
   * 需要记录的Header名称列表
   */
  private List<String> filterRecordHeaders;

  /**
   * 值的最大显示长度
   */
  private int filterRecordMaxValueLength = 100;

  private String fileMetaUrl;

  private String filePathRecordUrl;

  private String downloadBigBigFileUrl;
  /**
   * 是否开启当前云的速率限制
   */
  private boolean currentCloudRateLimit = true;
  /**
   * 每个企业限速 单位B/s
   */
  private int maxBytesByEnterprisePerSecond = 1024 * 1024 * 20;
  /**
   * 每个人限速 单位B/s
   */
  private int maxBytesByUserPerSecond = 1024 * 1024 * 5;
  /**
   * 每个文件限速 单位B/s
   */
  private int maxBytesByPathPerSecond = 1024 * 1024;
  /**
   * vip企业单独配置限速
   */
  private String vipEnterpriseMaxBytesPerSecond;

  // ====== CDN 文件相关配置 ======

  private long cdnMaxFileSize = 1024 * 1024 * 10; // CDN 文件最大大小，默认10MB

  private String cdnFileTypeCheckConfigName; // CDN 文件类型检查配置名
}
