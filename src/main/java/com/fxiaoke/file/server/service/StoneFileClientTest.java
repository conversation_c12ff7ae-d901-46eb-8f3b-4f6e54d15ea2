package com.fxiaoke.file.server.service;

import com.fxiaoke.stone.commons.StoneFileClient;
import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import org.springframework.stereotype.Component;

@Component
public class StoneFileClientTest {

  @Resource
  private StoneFileClient stoneFileClient;


  void testCacheFile() throws IOException {
    StoneUploadFile.Arg arg = new StoneUploadFile.Arg();
    arg.setEa("71554");
    arg.setEmployeeId(1181);
    arg.setResourceType(FileResourceEnum.C);
    arg.setBusiness("Test");
    arg.setExtension("png");
    arg.setFileSize(864408);
    StoneUploadFile.CacheConfig cacheConfig = new StoneUploadFile.CacheConfig();
    cacheConfig.setCachePath(Path.of("/opt/tomcat/11283912.png"));
    cacheConfig.setBufferSize(1024);
    cacheConfig.setMaxCacheSize(102400000);
    InputStream stream = Files.newInputStream(Path.of("/opt/tomcat/1.png"));
    StoneUploadFile.Result result = stoneFileClient.uploadFile(arg, cacheConfig, stream);
    System.out.println(result);
  }

}
