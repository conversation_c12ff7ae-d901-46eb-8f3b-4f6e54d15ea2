package com.fxiaoke.file.server.service.impl;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.common.Guard;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.S3Config;
import com.fxiaoke.file.server.domain.model.S3ObjectMetadata;
import com.fxiaoke.file.server.service.S3Service;
import com.fxiaoke.file.server.utils.ClusterEnvUtils;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigEiHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.moandjiezana.toml.Toml;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "S3ServiceImpl")
public class S3ServiceImpl implements S3Service {

  private static final String MODULE = "S3ServiceImpl";

  private static Guard guard;
  private final EIEAConverter eieaConverter;
  private final Cache<String, AmazonS3> clientCache;
  private final Cache<String, S3Config> s3ConfigCache;
  private final CmsPropertiesConfig cmsPropertiesConfig;

  public S3ServiceImpl(EIEAConverter eieaConverter, CmsPropertiesConfig cmsPropertiesConfig) {
    this.eieaConverter = eieaConverter;
    clientCache = CacheBuilder.newBuilder().maximumSize(300).build();

    s3ConfigCache = CacheBuilder.newBuilder().maximumSize(300).build();

    this.cmsPropertiesConfig = cmsPropertiesConfig;

    guard = new Guard(cmsPropertiesConfig.getS3EncryKey());

    ConfigFactory.getInstance().getConfig(cmsPropertiesConfig.getS3ConfigName(), config -> {
      log.info("start,load s3Config cache");
      Toml toml = new Toml().read(config.getString());
      loadCache(toml);
      log.info("end,load s3Config cache");
    });
  }

  private AmazonS3 getS3Client(String ea) {
    checkUserEnv(ea);
    AmazonS3 presentClient = clientCache.getIfPresent(ea);
    // 如果已经存在，直接返回
    if (presentClient != null) {
      return presentClient;
    }
    checkCommonClientUsageRightsByEnv(ea);
    return clientCache.getIfPresent(Constant.DEFAULT_S3_CLIENT);
  }

  @Override
  public S3ObjectMetadata getS3ObjectMetaInfo(String ea, String bucketName, String objectKey) {
    log.info("getS3ObjectMetaInfo start,ea={},bucketName={},objectKey={}", ea, bucketName,
        objectKey);
    AmazonS3 s3Client = getS3Client(ea);
    ObjectMetadata metadata = s3Client.getObjectMetadata(bucketName, objectKey);
    S3ObjectMetadata s3ObjectMetadata = toS3ObjectMetadata(bucketName, objectKey, metadata);
    log.info("getS3ObjectMetaInfo end,ea={},bucketName={},objectKey={},metadata={}", ea, bucketName,
        objectKey, s3ObjectMetadata);
    return s3ObjectMetadata;
  }

  @NotNull
  private static S3ObjectMetadata toS3ObjectMetadata(String bucketName, String objectKey,
      ObjectMetadata metadata) {
    S3ObjectMetadata s3ObjectMetadata = new S3ObjectMetadata();
    s3ObjectMetadata.setBucketName(bucketName);
    s3ObjectMetadata.setObjectKey(objectKey);
    s3ObjectMetadata.setETag(metadata.getETag());
    s3ObjectMetadata.setLastModified(metadata.getLastModified());
    s3ObjectMetadata.setContentLength(metadata.getContentLength());
    s3ObjectMetadata.setContentType(metadata.getContentType());
    s3ObjectMetadata.setCacheControl(metadata.getCacheControl());
    s3ObjectMetadata.setContentEncoding(metadata.getContentEncoding());
    s3ObjectMetadata.setContentDisposition(metadata.getContentDisposition());
    return s3ObjectMetadata;
  }

  // 检查用户是否为当前云环境用户
  private void checkUserEnv(String ea) {
    String tenantIdStr = String.valueOf(eieaConverter.enterpriseAccountToId(ea));
    boolean currentCloud = ConfigEiHelper.getInstance().isCurrentCloud(tenantIdStr);
    if (!currentCloud) {
      throw new FileServerException(MODULE + ".checkUserEnv",
          "Not current cloud user,forbidden access", ea);
    }
  }

  // 检查是否可以使用通用客户端（Facishare 环境禁用通用客户端）
  private void checkCommonClientUsageRightsByEnv(String ea) {
    // 如果是纷享云并且没有自己的s3Client,则抛出异常
    if (ClusterEnvUtils.isFacishareOrPrivateCluster()) {
      throw new FileServerException(MODULE + ".checkUserEnv",
          "Foneshare cloud user,Non-file exclusive,Disable S3", ea);
    }
  }

  private static ClientConfiguration initClientNetworkConfig(S3Config config) {
    ClientConfiguration clientNetworkConfig = new ClientConfiguration();
    // 设置连接超时时间
    clientNetworkConfig.setConnectionTimeout(config.getConnectionTimeout());
    // 设置读取超时时间
    clientNetworkConfig.setSocketTimeout(config.getSocketTimeout());
    // 设置最大连接数
    clientNetworkConfig.setMaxConnections(config.getMaxConnections());
    // 设置最大重试次数
    clientNetworkConfig.setMaxErrorRetry(config.getMaxErrorRetry());
    // 设置连接方式为HTTP或HTTPS
    clientNetworkConfig.setProtocol(config.getProtocol());

    // 设置代理,如果代理为空，则不设置代理,默认不设置代理
    if (config.isProxyEnabled()) {
      clientNetworkConfig.setProxyHost(config.getProxyHost());
      clientNetworkConfig.setProxyPort(Integer.parseInt(config.getProxyPort()));
    }

    // 设置签名算法,默认为AWS3Signer
    if (config.isSpecifySignerOverride()) {
      clientNetworkConfig.setSignerOverride(config.getType());
    }
    return clientNetworkConfig;
  }

  private AmazonS3 createS3Client(S3Config config) {
    log.info("create s3 client start,ea={},config={}", config.getEa(), config);
    // 使用企业账号作为缓存Client的key,不同的企业使用不同的Client
    AWSCredentials credentials = new BasicAWSCredentials(guard.decode(config.getAccessKeyId()),
        guard.decode(config.getAccessKeySecret()));
    EndpointConfiguration endpointConfig = new EndpointConfiguration(config.getEndpoint(),
        config.getRegion());
    ClientConfiguration clientNetworkConfig = initClientNetworkConfig(config);
    AmazonS3 client = AmazonS3ClientBuilder.standard()
        .withCredentials(new AWSStaticCredentialsProvider(credentials))
        .withClientConfiguration(clientNetworkConfig).withEndpointConfiguration(endpointConfig)
        .withPathStyleAccessEnabled(config.isPathStyleEnabled()).build();
    log.info("create s3 client end,ea={},config={}", config.getEa(), config);
    return client;
  }

  private void loadCache(Toml toml) {
    // 以配置文件  fs-stone-enterprise-cloud-toml 初始化S3客户端
    try {
      log.info("load s3Config cache start,toml={}", toml.toMap());
      for (Map.Entry<String, Object> entry : toml.entrySet()) {
        Toml configInfo = (Toml) entry.getValue();
        S3Config s3Config = new S3Config();
        s3Config.setEa(entry.getKey());
        // s3 客户端类型
        String s3ClientType = configInfo.getString("cloudType");
        s3Config.setCloudType(s3ClientType);
        // 仅自动注册 s3ClientType 类型为 standard_cloud 的客户端
        if (!s3ClientType.equals("standard_cloud")) {
          log.info("ea={} s3ClientType={} not standard_cloud,skip", s3Config.getEa(), s3ClientType);
          continue;
        }
        s3Config.setBucket(configInfo.getString("bucket"));
        s3Config.setTempBucket(configInfo.getString("tempBucket"));

        s3Config.setAccessKeyId(configInfo.getString("accessKeyId"));
        s3Config.setAccessKeySecret(configInfo.getString("accessKeySecret"));
        s3Config.setType(configInfo.getString("type", ""));

        s3Config.setEndpoint(configInfo.getString("endPoint"));
        s3Config.setRegion(configInfo.getString("region", ""));
        s3Config.setPathStyleEnabled(configInfo.getBoolean("pathStyleEnabled", false));
        s3Config.setProxyHost(configInfo.getString("proxyHost", ""));
        s3Config.setProxyPort(configInfo.getString("proxyPort", ""));
        s3Config.setConnectionTimeout(
            Integer.parseInt(configInfo.getString("connectionTimeout", "10000")));
        s3Config.setSocketTimeout(Integer.parseInt(configInfo.getString("socketTimeout", "50000")));
        s3Config.setMaxConnections(Integer.parseInt(configInfo.getString("maxConnections", "50")));
        s3Config.setMaxErrorRetry(Integer.parseInt(configInfo.getString("maxErrorRetry", "3")));

        s3Config.setSharedDisksDir(configInfo.getString("sharedDisksDir", Constant.FILE_PATH));
        s3Config.setMinSegmentSize(
            Integer.parseInt(configInfo.getString("minSegmentSize", "5242880")));
        s3Config.setReadLimit(Integer.parseInt(configInfo.getString("readLimit", "1048576")));

        // 检查配置是否正确
        s3Config.checkConfig();

        // 如果配置没有变化,则不更新缓存
        S3Config oldS3Config = s3ConfigCache.getIfPresent(s3Config.getEa());
        if (clientCache.getIfPresent(s3Config.getEa()) != null && oldS3Config != null
            && oldS3Config.hashCode() == s3Config.hashCode()) {
          log.info("s3Config is same,ea:{} s3Config cache not refresh", s3Config.getEa());
          continue;
        }

        s3ConfigCache.put(entry.getKey(), s3Config);
        log.info("add or update ea={} cache s3Config,config={}", s3Config.getEa(), s3Config);
        AmazonS3 client = createS3Client(s3Config);
        clientCache.put(s3Config.getEa(), client);
        log.info("add or update ea={} cache s3Client", s3Config.getEa());
      }
    } catch (Exception e) {
      throw new FileServerException(e, MODULE + ".loadCache", ErInfo.S3_CONFIG_LOAD_ERROR, toml);
    }
    // 不是纷享云,并且默认的s3Client为空,则抛出异常
    if (!ClusterEnvUtils.isFacishareOrPrivateCluster()
        && s3ConfigCache.getIfPresent(Constant.DEFAULT_S3_CLIENT) == null) {
      throw new FileServerException(MODULE + ".loadCache",
          "default s3Client=" + cmsPropertiesConfig.getS3ConfigName() + " not found,configName="
              + Constant.DEFAULT_S3_CONFIG_NAME);
    }
    log.info("load s3Config cache end,s3Config.size={},s3Client.size={}", s3ConfigCache.size(),
        clientCache.size());
  }
}
