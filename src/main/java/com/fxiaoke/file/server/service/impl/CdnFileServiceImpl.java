package com.fxiaoke.file.server.service.impl;

import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.dao.mongo.CdnFileMetaDao;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.entity.NFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import com.fxiaoke.file.server.help.FileTypeCheckHelp;
import com.fxiaoke.file.server.service.AsmService;
import com.fxiaoke.file.server.service.CdnFileService;
import com.fxiaoke.file.server.service.FileMetaService;
import com.fxiaoke.file.server.service.S3Service;
import com.fxiaoke.file.server.utils.CodingUtil;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import org.springframework.stereotype.Service;

@Service
public class CdnFileServiceImpl implements CdnFileService {

  private final static String MODULE = "CdnFileService";

  private final S3Service s3Service;

  private final StoneProxyApi nFile;

  private final AsmService asmService;

  private final CmsPropertiesConfig config;

  private final CdnFileMetaDao cdnFileMetaDao;

  private final FileMetaService fileMetaService;

  private final FileTypeCheckHelp fileTypeCheckHelp;

  public CdnFileServiceImpl(S3Service s3Service, StoneProxyApi nFile, AsmService asmService,
      CmsPropertiesConfig config, CdnFileMetaDao cdnFileMetaDao, FileMetaService fileMetaService,
      FileTypeCheckHelp fileTypeCheckHelp) {
    this.s3Service = s3Service;
    this.nFile = nFile;
    this.asmService = asmService;
    this.config = config;
    this.cdnFileMetaDao = cdnFileMetaDao;
    this.fileMetaService = fileMetaService;
    this.fileTypeCheckHelp = fileTypeCheckHelp;
  }

  @Override
  public String pathToCdnFile(PathToCdnFileReq req) {

    try {
      // 从 stoneStorageService 获取文件元数据
      int tenantId = Math.toIntExact(req.getTenantId());
      int employeeId = Math.toIntExact(req.getEmployeeId());
      String ea = asmService.getEa(tenantId);
      NFileMeta nFileMeta = fileMetaService.find(ea, req.getPath());
      long size = nFileMeta.getSize();

      // 校验文件大小
      CodingUtil.throwIfFalse(size > config.getCdnMaxFileSize(),
          "PathToCdnFileReq.size exceeds maximum allowed size: " + size);

      // 从 nFile 获取文件流
      try (BufferedInputStream stream = new BufferedInputStream(getNCFileByStone(ea, employeeId, req.getPath(), nFileMeta.getExtension()))) {
        fileTypeCheckHelp.check(stream, ea, size, nFileMeta.getExtension());
      }

      // 从流中读取文件魔数判断文件真实类型，仅支持配置的类型转换为 CDN 文件

      // s3Service 上传到 S3

      // s3Service 获取文件hashcode

      // cdnFileMetaDao 保存文件元数据到 MongoDB

      // 返回 CDN 文件的 请求地址（无域名） 即 cdnReqPath

      return null;
    } catch (Exception e) {
      // 处理异常情况
      throw new FileServerException(MODULE, ErInfo.ACCESS_FILE_FAIL_BY_STONE, e, req.getPath());
    }
  }

  public InputStream getNCFileByStone(String ea, Integer employeeId, String path,
      String extension) {

    try {

      StoneFileDownloadRequest request = new StoneFileDownloadRequest();
      request.setEa(ea);
      request.setEmployeeId(employeeId);
      request.setPath(path);
      request.setBusiness(Constant.BUSINESS);
      request.setSecurityGroup(Constant.DEFAULT_SECURITY_GROUP);
      request.setFileType(extension);
      request.setCancelRemoteThumb(true);
      return nFile.downloadStream(request);

    } catch (FRestClientException e) {
      // Stone服务访问失败情况
      throw new FileServerException(e, MODULE, ErInfo.ACCESS_FILE_FAIL_BY_STONE, ea, employeeId,
          path, extension);
    }
  }


}
