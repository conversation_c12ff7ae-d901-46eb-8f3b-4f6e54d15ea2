package com.fxiaoke.file.server.help;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.*;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * VirtualThreadPoolHelper 单元测试
 * 使用 JUnit 5 和 Mockito 进行测试
 * 目标：100% 代码覆盖率
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("VirtualThreadPoolHelper 测试")
class VirtualThreadPoolHelperTest {

    private VirtualThreadPoolHelper virtualThreadPoolHelper;
    private ExecutorService mockExecutorService;

    @BeforeEach
    void setUp() {
        virtualThreadPoolHelper = new VirtualThreadPoolHelper();
        mockExecutorService = mock(ExecutorService.class);
    }

    @AfterEach
    void tearDown() {
        if (virtualThreadPoolHelper != null && virtualThreadPoolHelper.isAvailable()) {
            virtualThreadPoolHelper.destroy();
        }
    }

    @Nested
    @DisplayName("初始化测试")
    class InitializationTests {

        @Test
        @DisplayName("成功初始化虚拟线程池")
        void testSuccessfulInitialization() {
            // 使用 MockedStatic 来模拟 Executors.newVirtualThreadPerTaskExecutor()
            try (MockedStatic<Executors> executorsMock = mockStatic(Executors.class)) {
                ExecutorService mockVirtualExecutor = mock(ExecutorService.class);
                executorsMock.when(Executors::newVirtualThreadPerTaskExecutor)
                        .thenReturn(mockVirtualExecutor);

                // 执行初始化
                virtualThreadPoolHelper.init();

                // 验证初始化成功
                assertTrue(virtualThreadPoolHelper.isAvailable());
                assertNotNull(virtualThreadPoolHelper.getVirtualThreadExecutor());
            }
        }

        @Test
        @DisplayName("初始化失败时使用降级方案")
        void testInitializationFallback() {
            // 使用 MockedStatic 来模拟初始化失败
            try (MockedStatic<Executors> executorsMock = mockStatic(Executors.class)) {
                ExecutorService mockCachedExecutor = mock(ExecutorService.class);

                // 模拟虚拟线程创建失败
                executorsMock.when(Executors::newVirtualThreadPerTaskExecutor)
                        .thenThrow(new RuntimeException("虚拟线程不支持"));

                // 模拟降级到普通线程池
                executorsMock.when(Executors::newCachedThreadPool)
                        .thenReturn(mockCachedExecutor);

                // 执行初始化
                virtualThreadPoolHelper.init();

                // 验证降级成功
                assertTrue(virtualThreadPoolHelper.isAvailable());
                assertNotNull(virtualThreadPoolHelper.getVirtualThreadExecutor());
            }
        }
    }

    @Nested
    @DisplayName("销毁测试")
    class DestroyTests {

        @Test
        @DisplayName("正常销毁线程池")
        void testNormalDestroy() throws InterruptedException {
            // 设置 mock 执行器
            setMockExecutor();
            when(mockExecutorService.isShutdown()).thenReturn(false);
            when(mockExecutorService.awaitTermination(5, TimeUnit.SECONDS)).thenReturn(true);

            // 执行销毁
            virtualThreadPoolHelper.destroy();

            // 验证调用
            verify(mockExecutorService).shutdown();
            verify(mockExecutorService).awaitTermination(5, TimeUnit.SECONDS);
            verify(mockExecutorService, never()).shutdownNow();
        }

        @Test
        @DisplayName("强制销毁线程池")
        void testForceDestroy() throws InterruptedException {
            // 设置 mock 执行器
            setMockExecutor();
            when(mockExecutorService.isShutdown()).thenReturn(false);
            when(mockExecutorService.awaitTermination(5, TimeUnit.SECONDS)).thenReturn(false);

            // 执行销毁
            virtualThreadPoolHelper.destroy();

            // 验证调用
            verify(mockExecutorService).shutdown();
            verify(mockExecutorService).awaitTermination(5, TimeUnit.SECONDS);
            verify(mockExecutorService).shutdownNow();
        }

        @Test
        @DisplayName("销毁时被中断")
        void testDestroyInterrupted() throws InterruptedException {
            // 设置 mock 执行器
            setMockExecutor();
            when(mockExecutorService.isShutdown()).thenReturn(false);
            when(mockExecutorService.awaitTermination(5, TimeUnit.SECONDS))
                    .thenThrow(new InterruptedException("测试中断"));

            // 执行销毁
            virtualThreadPoolHelper.destroy();

            // 验证调用
            verify(mockExecutorService).shutdown();
            verify(mockExecutorService).shutdownNow();
            assertTrue(Thread.interrupted()); // 验证中断状态被重置
        }

        @Test
        @DisplayName("销毁已关闭的线程池")
        void testDestroyAlreadyShutdown() {
            // 设置 mock 执行器
            setMockExecutor();
            when(mockExecutorService.isShutdown()).thenReturn(true);

            // 执行销毁
            virtualThreadPoolHelper.destroy();

            // 验证不会调用 shutdown
            verify(mockExecutorService, never()).shutdown();
        }

        @Test
        @DisplayName("销毁空线程池")
        void testDestroyNullExecutor() {
            // 不设置执行器，保持为 null

            // 执行销毁（不应该抛出异常）
            assertDoesNotThrow(() -> virtualThreadPoolHelper.destroy());
        }
    }

    @Nested
    @DisplayName("任务提交测试")
    class TaskSubmissionTests {

        @BeforeEach
        void setUpExecutor() {
            setMockExecutor();
        }

        @Test
        @DisplayName("提交 Callable 任务成功")
        void testSubmitCallableTaskSuccess() throws Exception {
            Future<String> mockFuture = mock(Future.class);
            when(mockExecutorService.submit(any(Callable.class))).thenReturn(mockFuture);

            Callable<String> task = () -> "测试结果";
            Future<String> result = virtualThreadPoolHelper.submitTask(task);

            assertNotNull(result);
            verify(mockExecutorService).submit(task);
        }

        @Test
        @DisplayName("提交 Callable 任务失败")
        void testSubmitCallableTaskFailure() {
            when(mockExecutorService.submit(any(Callable.class)))
                    .thenThrow(new RuntimeException("提交失败"));

            Callable<String> task = () -> "测试结果";

            RuntimeException exception = assertThrows(RuntimeException.class,
                    () -> virtualThreadPoolHelper.submitTask(task));

            assertEquals("提交虚拟线程任务失败", exception.getMessage());
        }

        @Test
        @DisplayName("提交 Runnable 任务成功")
        void testSubmitRunnableTaskSuccess() {
            Future<?> mockFuture = CompletableFuture.completedFuture(null);
            doReturn(mockFuture).when(mockExecutorService).submit(any(Runnable.class));

            Runnable task = () -> System.out.println("测试任务");
            Future<?> result = virtualThreadPoolHelper.submitTask(task);

            assertNotNull(result);
            verify(mockExecutorService).submit(task);
        }

        @Test
        @DisplayName("提交 Runnable 任务失败")
        void testSubmitRunnableTaskFailure() {
            doThrow(new RuntimeException("提交失败"))
                    .when(mockExecutorService).submit(any(Runnable.class));

            Runnable task = () -> System.out.println("测试任务");

            RuntimeException exception = assertThrows(RuntimeException.class,
                    () -> virtualThreadPoolHelper.submitTask(task));

            assertEquals("提交虚拟线程任务失败", exception.getMessage());
        }

        @Test
        @DisplayName("执行异步任务成功")
        void testExecuteAsyncSuccess() {
            Runnable task = () -> System.out.println("异步任务");

            assertDoesNotThrow(() -> virtualThreadPoolHelper.executeAsync(task));
            verify(mockExecutorService).execute(task);
        }

        @Test
        @DisplayName("执行异步任务失败")
        void testExecuteAsyncFailure() {
            doThrow(new RuntimeException("执行失败"))
                    .when(mockExecutorService).execute(any(Runnable.class));

            Runnable task = () -> System.out.println("异步任务");

            RuntimeException exception = assertThrows(RuntimeException.class,
                    () -> virtualThreadPoolHelper.executeAsync(task));

            assertEquals("执行虚拟线程任务失败", exception.getMessage());
        }
    }

    @Nested
    @DisplayName("CompletableFuture 测试")
    class CompletableFutureTests {

        @BeforeEach
        void setUpExecutor() {
            setMockExecutor();
        }

        @Test
        @DisplayName("supplyAsync 成功")
        void testSupplyAsyncSuccess() {
            Supplier<String> supplier = () -> "测试结果";

            CompletableFuture<String> result = virtualThreadPoolHelper.supplyAsync(supplier);

            assertNotNull(result);
            assertFalse(result.isDone()); // 异步执行，可能还未完成
        }

        @Test
        @DisplayName("runAsync 成功")
        void testRunAsyncSuccess() {
            Runnable runnable = () -> System.out.println("异步运行");

            CompletableFuture<Void> result = virtualThreadPoolHelper.runAsync(runnable);

            assertNotNull(result);
        }

        @Test
        @DisplayName("allOf 测试")
        void testAllOf() {
            CompletableFuture<String> future1 = CompletableFuture.completedFuture("结果1");
            CompletableFuture<String> future2 = CompletableFuture.completedFuture("结果2");

            CompletableFuture<Void> result = virtualThreadPoolHelper.allOf(future1, future2);

            assertNotNull(result);
            assertTrue(result.isDone());
        }

        @Test
        @DisplayName("anyOf 测试")
        void testAnyOf() {
            CompletableFuture<String> future1 = CompletableFuture.completedFuture("结果1");
            CompletableFuture<String> future2 = CompletableFuture.completedFuture("结果2");

            CompletableFuture<Object> result = virtualThreadPoolHelper.anyOf(future1, future2);

            assertNotNull(result);
            assertTrue(result.isDone());
        }
    }

    @Nested
    @DisplayName("虚拟线程创建测试")
    class VirtualThreadCreationTests {

        @Test
        @DisplayName("创建虚拟线程成功")
        void testStartVirtualThreadSuccess() {
            try (MockedStatic<Thread> threadMock = mockStatic(Thread.class)) {
                Thread.Builder mockBuilder = mock(Thread.Builder.class);
                Thread mockThread = mock(Thread.class);

                threadMock.when(Thread::ofVirtual).thenReturn(mockBuilder);
                when(mockBuilder.start(any(Runnable.class))).thenReturn(mockThread);
                when(mockThread.getName()).thenReturn("virtual-thread-1");

                Runnable task = () -> System.out.println("虚拟线程任务");
                Thread result = virtualThreadPoolHelper.startVirtualThread(task);

                assertNotNull(result);
                verify(mockBuilder).start(task);
            }
        }

        @Test
        @DisplayName("创建虚拟线程失败")
        void testStartVirtualThreadFailure() {
            try (MockedStatic<Thread> threadMock = mockStatic(Thread.class)) {
                Thread.Builder mockBuilder = mock(Thread.Builder.class);

                threadMock.when(Thread::ofVirtual).thenReturn(mockBuilder);
                when(mockBuilder.start(any(Runnable.class)))
                        .thenThrow(new RuntimeException("创建失败"));

                Runnable task = () -> System.out.println("虚拟线程任务");

                RuntimeException exception = assertThrows(RuntimeException.class,
                        () -> virtualThreadPoolHelper.startVirtualThread(task));

                assertEquals("创建虚拟线程失败", exception.getMessage());
            }
        }

        @Test
        @DisplayName("创建命名虚拟线程成功")
        void testStartNamedVirtualThreadSuccess() {
            try (MockedStatic<Thread> threadMock = mockStatic(Thread.class)) {
                Thread.Builder mockBuilder = mock(Thread.Builder.class);
                Thread mockThread = mock(Thread.class);

                threadMock.when(Thread::ofVirtual).thenReturn(mockBuilder);
                when(mockBuilder.name("test-thread")).thenReturn(mockBuilder);
                when(mockBuilder.start(any(Runnable.class))).thenReturn(mockThread);
                when(mockThread.getName()).thenReturn("test-thread");

                Runnable task = () -> System.out.println("命名虚拟线程任务");
                Thread result = virtualThreadPoolHelper.startVirtualThread("test-thread", task);

                assertNotNull(result);
                verify(mockBuilder).name("test-thread");
                verify(mockBuilder).start(task);
            }
        }

        @Test
        @DisplayName("创建命名虚拟线程失败")
        void testStartNamedVirtualThreadFailure() {
            try (MockedStatic<Thread> threadMock = mockStatic(Thread.class)) {
                Thread.Builder mockBuilder = mock(Thread.Builder.class);

                threadMock.when(Thread::ofVirtual).thenReturn(mockBuilder);
                when(mockBuilder.name("test-thread")).thenReturn(mockBuilder);
                when(mockBuilder.start(any(Runnable.class)))
                        .thenThrow(new RuntimeException("创建失败"));

                Runnable task = () -> System.out.println("命名虚拟线程任务");

                RuntimeException exception = assertThrows(RuntimeException.class,
                        () -> virtualThreadPoolHelper.startVirtualThread("test-thread", task));

                assertEquals("创建命名虚拟线程失败", exception.getMessage());
            }
        }
    }

    @Nested
    @DisplayName("状态检查测试")
    class StatusCheckTests {

        @Test
        @DisplayName("线程池可用")
        void testIsAvailableTrue() {
            setMockExecutor();
            when(mockExecutorService.isShutdown()).thenReturn(false);

            assertTrue(virtualThreadPoolHelper.isAvailable());
        }

        @Test
        @DisplayName("线程池已关闭")
        void testIsAvailableFalseShutdown() {
            setMockExecutor();
            when(mockExecutorService.isShutdown()).thenReturn(true);

            assertFalse(virtualThreadPoolHelper.isAvailable());
        }

        @Test
        @DisplayName("线程池为空")
        void testIsAvailableFalseNull() {
            // 不设置执行器，保持为 null

            assertFalse(virtualThreadPoolHelper.isAvailable());
        }

        @Test
        @DisplayName("获取虚拟线程执行器")
        void testGetVirtualThreadExecutor() {
            setMockExecutor();

            ExecutorService result = virtualThreadPoolHelper.getVirtualThreadExecutor();

            assertSame(mockExecutorService, result);
        }
    }

    @Nested
    @DisplayName("集成测试")
    class IntegrationTests {

        @Test
        @DisplayName("完整生命周期测试")
        void testCompleteLifecycle() {
            // 初始化
            virtualThreadPoolHelper.init();
            assertTrue(virtualThreadPoolHelper.isAvailable());

            // 提交任务
            Future<String> future = virtualThreadPoolHelper.submitTask(() -> "集成测试");
            assertNotNull(future);

            // 销毁
            virtualThreadPoolHelper.destroy();
        }

        @Test
        @DisplayName("实际异步任务执行测试")
        void testRealAsyncExecution() throws Exception {
            // 使用真实的线程池进行集成测试
            virtualThreadPoolHelper.init();

            CountDownLatch latch = new CountDownLatch(1);
            StringBuilder result = new StringBuilder();

            // 提交异步任务
            virtualThreadPoolHelper.executeAsync(() -> {
                result.append("异步任务执行完成");
                latch.countDown();
            });

            // 等待任务完成
            assertTrue(latch.await(5, TimeUnit.SECONDS));
            assertEquals("异步任务执行完成", result.toString());
        }
    }

    /**
     * 设置 mock 执行器的辅助方法
     */
    private void setMockExecutor() {
        try {
            // 使用反射设置私有字段
            java.lang.reflect.Field executorField = VirtualThreadPoolHelper.class
                    .getDeclaredField("virtualThreadExecutor");
            executorField.setAccessible(true);
            executorField.set(virtualThreadPoolHelper, mockExecutorService);

            java.lang.reflect.Field asyncExecutorField = VirtualThreadPoolHelper.class
                    .getDeclaredField("virtualThreadAsyncExecutor");
            asyncExecutorField.setAccessible(true);
            asyncExecutorField.set(virtualThreadPoolHelper, mockExecutorService);
        } catch (Exception e) {
            throw new RuntimeException("设置 mock 执行器失败", e);
        }
    }
}