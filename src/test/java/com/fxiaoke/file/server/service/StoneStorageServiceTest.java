package com.fxiaoke.file.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.AcFileInfo;
import com.fxiaoke.file.server.domain.model.api.UpFileInfo;
import com.fxiaoke.file.server.domain.model.api.UserInfo;
import com.fxiaoke.file.server.help.GrayHelper;
import com.fxiaoke.file.server.utils.ClusterEnvUtils;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class StoneStorageServiceTest {

    @Mock
    private GrayHelper grayHelper;
    
    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;
    
    @Mock
    private StoneProxyApi stoneProxyApi;
    
    @Mock
    private AtsService atsService;
    
    @Mock
    private GFileStorageService gFileStorageService;
    
    @Mock
    private AFileStorageService aFileStorageService;
    
    @Mock
    private IAvatarService avatarService;

    @Mock
    private ImaginaryService imaginaryService;
    
    @Mock
    private CrossCloudService crossCloudService;
    
    private StoneStorageService stoneStorageService;

    @BeforeEach
    void setUp() {
        // mock CmsPropertiesConfig
        when(cmsPropertiesConfig.getAtsServerNodeHost()).thenReturn("ats.example.com");
        
        stoneStorageService = new StoneStorageService(
            stoneProxyApi,
            gFileStorageService,
            aFileStorageService,
            grayHelper,
            cmsPropertiesConfig,
            atsService,
            imaginaryService,
            crossCloudService
        );
        
        // 设置 avatarService
        ReflectionTestUtils.setField(stoneStorageService, "avatarService", avatarService);
    }

    @Test
    void uploadSingleFile_NFile_Success() throws Exception {
        // 准备测试数据
        UpFileInfo upFileInfo = new UpFileInfo();
        upFileInfo.setEa("71554");
        upFileInfo.setEmployeeId(1181);
        upFileInfo.setFileName("test.jpg");
        upFileInfo.setExtension("jpg");
        upFileInfo.setFileSize(1024);
        upFileInfo.setResourceType("N");
        
        String expectedPath = "N_202406_13_test";
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        
        // mock StoneProxyApi
        StoneFileUploadResponse mockResponse = new StoneFileUploadResponse();
        mockResponse.setPath(expectedPath);
        when(stoneProxyApi.uploadByStream(anyString(), any(StoneFileUploadRequest.class), any(InputStream.class)))
            .thenReturn(mockResponse);
        
        // 执行测试
        String result = stoneStorageService.uploadSingleFile(upFileInfo, inputStream);
        
        // 验证结果
        assertEquals(expectedPath, result);
        verify(stoneProxyApi).uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(InputStream.class));
    }

    @Test
    void uploadSingleFile_NFile_Failure() throws Exception {
        // 准备测试数据
        UpFileInfo upFileInfo = new UpFileInfo();
        upFileInfo.setEa("71554");
        upFileInfo.setEmployeeId(1181);
        upFileInfo.setFileName("test.jpg");
        upFileInfo.setExtension("jpg");
        upFileInfo.setFileSize(1024);
        upFileInfo.setResourceType("N");
        
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        
        // mock StoneProxyApi抛出异常
        when(stoneProxyApi.uploadByStream(anyString(), any(StoneFileUploadRequest.class), any(InputStream.class)))
            .thenThrow(FRestClientException.class);
        
        // 验证抛出预期的异常
        FileServerException exception = assertThrows(FileServerException.class,
            () -> stoneStorageService.uploadSingleFile(upFileInfo, inputStream));
        
        // 验证异常信息
        assertEquals(ErInfo.UPLOAD_FILE_TO_STORAGE_FAIL.getCode(), exception.getCode());
    }

    @Test
    void getFile_NormalFile_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEnterpriseAccount("71554");
        acFileInfo.setUserInfo(userInfo);
        
        // mock相关配置
        when(cmsPropertiesConfig.isAtsCacheEnable()).thenReturn(false);
        
        // mock StoneProxyApi
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        when(stoneProxyApi.downloadStream(any())).thenReturn(expectedStream);
        
        // 执行测试
        InputStream result = stoneStorageService.getFile(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(stoneProxyApi).downloadStream(any());
    }

    @Test
    void getFile_AvatarFile_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("A_202406_13_test");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileAcEnterpriseAccount("71554");
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock AFileStorageService
        ADownloadFile.Result mockResult = new ADownloadFile.Result();
        mockResult.setData("test".getBytes());
        when(aFileStorageService.downloadFile(any())).thenReturn(mockResult);
        
        // 执行测试
        InputStream result = stoneStorageService.getFile(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(aFileStorageService).downloadFile(any());
    }

    @Test
    void getFile_GroupFile_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("G_202406_13_test");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock GFileStorageService
        GFileDownload.Result mockResult = new GFileDownload.Result();
        mockResult.data = "test".getBytes();
        when(gFileStorageService.downloadFile(any())).thenReturn(mockResult);
        
        // 执行测试
        InputStream result = stoneStorageService.getFile(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(gFileStorageService).downloadFile(any());
    }

    @Test
    void uploadTcFile_Success() throws Exception {
        // 准备测试数据
        UpFileInfo upFileInfo = new UpFileInfo();
        upFileInfo.setEa("71554");
        upFileInfo.setEmployeeId(1181);
        upFileInfo.setFileName("test.jpg");
        upFileInfo.setExtension("jpg");
        upFileInfo.setFileSize(1024);
        upFileInfo.setResourceType("TC");
        
        String expectedPath = "TC_202406_13_test";
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        
        // mock StoneProxyApi
        StoneFileUploadResponse mockResponse = new StoneFileUploadResponse();
        mockResponse.setPath(expectedPath);
        when(stoneProxyApi.tempFileUploadByStream(anyString(), any(StoneFileUploadRequest.class), any(InputStream.class)))
            .thenReturn(mockResponse);
        
        // 执行测试
        String result = stoneStorageService.uploadSingleFile(upFileInfo, inputStream);
        
        // 验证结果
        assertEquals(expectedPath, result);
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(InputStream.class));
    }

    @Test
    void uploadCFile_Success() throws Exception {
        // 准备测试数据
        UpFileInfo upFileInfo = new UpFileInfo();
        upFileInfo.setEa("71554");
        upFileInfo.setEmployeeId(1181);
        upFileInfo.setFileName("test.jpg");
        upFileInfo.setExtension("jpg");
        upFileInfo.setFileSize(1024);
        upFileInfo.setResourceType("C");
        
        String expectedPath = "C_202406_13_test";
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        
        // mock StoneProxyApi
        StoneFileUploadResponse mockResponse = new StoneFileUploadResponse();
        mockResponse.setPath(expectedPath);
        when(stoneProxyApi.uploadByStream(anyString(), any(StoneFileUploadRequest.class), any(InputStream.class)))
            .thenReturn(mockResponse);
        
        // 执行测试
        String result = stoneStorageService.uploadSingleFile(upFileInfo, inputStream);
        
        // 验证结果
        assertEquals(expectedPath, result);
        verify(stoneProxyApi).uploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(InputStream.class));
    }

    @Test
    void uploadTnFile_Success() throws Exception {
        // 准备测试数据
        UpFileInfo upFileInfo = new UpFileInfo();
        upFileInfo.setEa("71554");
        upFileInfo.setEmployeeId(1181);
        upFileInfo.setFileName("test.jpg");
        upFileInfo.setExtension("jpg");
        upFileInfo.setFileSize(1024);
        upFileInfo.setResourceType("TN");
        
        String expectedPath = "TN_202406_13_test";
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        
        // mock StoneProxyApi
        StoneFileUploadResponse mockResponse = new StoneFileUploadResponse();
        mockResponse.setPath(expectedPath);
        when(stoneProxyApi.tempFileUploadByStream(anyString(), any(StoneFileUploadRequest.class), any(InputStream.class)))
            .thenReturn(mockResponse);
        
        // 执行测试
        String result = stoneStorageService.uploadSingleFile(upFileInfo, inputStream);
        
        // 验证结果
        assertEquals(expectedPath, result);
        verify(stoneProxyApi).tempFileUploadByStream(eq("n"), any(StoneFileUploadRequest.class), any(InputStream.class));
    }

    @Test
    void getFile_ImageWithAtsCache_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test.jpg");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEnterpriseAccount("71554");
        acFileInfo.setUserInfo(userInfo);
        acFileInfo.setExtension("jpg");
        
        // mock相关配置
        when(cmsPropertiesConfig.isAtsCacheEnable()).thenReturn(true);
        when(grayHelper.isEnableATSCache(anyString())).thenReturn(true);
        
        // mock AtsService
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        when(atsService.getNCImageByATS(any())).thenReturn(expectedStream);
        
        // 执行测试
        InputStream result = stoneStorageService.getFile(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(atsService).getNCImageByATS(any());
    }

    @Test
    void getFile_ImageWithAtsCache_FallbackToStone() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test.jpg");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEnterpriseAccount("71554");
        acFileInfo.setUserInfo(userInfo);
        acFileInfo.setExtension("jpg");
        
        // mock相关配置
        when(cmsPropertiesConfig.isAtsCacheEnable()).thenReturn(true);
        when(grayHelper.isEnableATSCache(anyString())).thenReturn(true);
        
        // mock AtsService抛出异常
        when(atsService.getNCImageByATS(any())).thenThrow(new IOException("ATS error"));
        
        // mock StoneProxyApi
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        when(stoneProxyApi.downloadStream(any())).thenReturn(expectedStream);
        
        // 执行测试
        InputStream result = stoneStorageService.getFile(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(atsService).getNCImageByATS(any());
        verify(stoneProxyApi).downloadStream(any());
    }

    @Test
    void getAvatarFile_Success() throws Exception {
        // 准备测试数据
        String path = "avatar_123";
        OutputStream outputStream = mock(OutputStream.class);
        
        // mock ClusterEnvUtils
        try (MockedStatic<ClusterEnvUtils> mockedStatic = mockStatic(ClusterEnvUtils.class)) {
            mockedStatic.when(ClusterEnvUtils::isExistsAvatarFileSystem).thenReturn(true);
            
            // 执行测试
            stoneStorageService.getAvatarFile(path, outputStream);
            
            // 验证结果
            verify(avatarService).downloadAvatarToOutStream(path, outputStream);
        }
    }

    @Test
    void getAvatarFile_NoAvatarSystem() {
        // 准备测试数据
        String path = "avatar_123";
        OutputStream outputStream = mock(OutputStream.class);
        
        // mock ClusterEnvUtils
        try (MockedStatic<ClusterEnvUtils> mockedStatic = mockStatic(ClusterEnvUtils.class)) {
            mockedStatic.when(ClusterEnvUtils::isExistsAvatarFileSystem).thenReturn(false);
            
            // 验证抛出预期的异常
            FileServerException exception = assertThrows(FileServerException.class,
                () -> stoneStorageService.getAvatarFile(path, outputStream));
            
            // 验证异常信息
            assertEquals(ErInfo.AVATAR_FILE_SYSTEM_NOT_EXISTS.getCode(), exception.getCode());
        }
    }

    @Test
    void getNCFileByStone_NormalFile_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test");
        acFileInfo.setExtension("txt");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock StoneProxyApi
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        when(stoneProxyApi.downloadStream(any())).thenReturn(expectedStream);
        
        // 执行测试
        InputStream result = stoneStorageService.getNCFileByStone(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(stoneProxyApi).downloadStream(any());
    }

    @Test
    void getNCFileByStone_ImageFile_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test");
        acFileInfo.setExtension("jpg");
        acFileInfo.setWidth(100);
        acFileInfo.setHeight(100);
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock StoneProxyApi
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        when(stoneProxyApi.getImageThumb(any())).thenReturn(expectedStream);
        
        // 执行测试
        InputStream result = stoneStorageService.getNCFileByStone(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(stoneProxyApi).getImageThumb(any());
    }

    @Test
    void getNCFileByStone_ImageOriginal_Success() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test");
        acFileInfo.setExtension("jpg");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock StoneProxyApi
        InputStream expectedStream = new ByteArrayInputStream("test".getBytes());
        when(stoneProxyApi.downloadStream(any())).thenReturn(expectedStream);
        
        // 执行测试
        InputStream result = stoneStorageService.getNCFileByStone(acFileInfo);
        
        // 验证结果
        assertNotNull(result);
        verify(stoneProxyApi).downloadStream(any());
    }

    @Test
    void getNCFileByStone_ExpiredFile_ThrowsException() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test");
        acFileInfo.setExtension("txt");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock StoneProxyApi抛出异常
        FRestClientException mockException = mock(FRestClientException.class);
        when(mockException.getCode()).thenReturn("400");
        when(stoneProxyApi.downloadStream(any())).thenThrow(mockException);
        
        // 验证抛出预期的异常
        FileServerException exception = assertThrows(FileServerException.class,
            () -> stoneStorageService.getNCFileByStone(acFileInfo));
        
        // 验证异常信息
        assertEquals(ErInfo.ACCESS_EXPIRED_OR_NOT_EXIST_FILE.getCode(), exception.getCode());
    }

    @Test
    void getNCFileByStone_OtherError_ThrowsException() throws Exception {
        // 准备测试数据
        AcFileInfo acFileInfo = new AcFileInfo();
        acFileInfo.setPath("N_202406_13_test");
        acFileInfo.setExtension("txt");
        UserInfo userInfo = new UserInfo();
        userInfo.setFileOwnerEnterpriseAccount("71554");
        userInfo.setFileAcEmployeeId(1181);
        acFileInfo.setUserInfo(userInfo);
        
        // mock StoneProxyApi抛出异常
        FRestClientException mockException = mock(FRestClientException.class);
        when(mockException.getCode()).thenReturn("500");
        when(stoneProxyApi.downloadStream(any())).thenThrow(mockException);
        
        // 验证抛出预期的异常
        FileServerException exception = assertThrows(FileServerException.class,
            () -> stoneStorageService.getNCFileByStone(acFileInfo));
        
        // 验证异常信息
        assertEquals(ErInfo.ACCESS_FILE_FAIL_BY_STONE.getCode(), exception.getCode());
    }
} 