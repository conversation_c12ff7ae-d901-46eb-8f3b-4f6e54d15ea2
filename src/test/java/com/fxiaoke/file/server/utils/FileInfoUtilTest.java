package com.fxiaoke.file.server.utils;

import com.fxiaoke.common.Pair;
import org.junit.Test;
import static org.junit.Assert.*;

public class FileInfoUtilTest {

    @Test
    public void testGetMimeTypeByExtension() {
        // 测试常见文件类型
        assertEquals("text/plain", FileInfoUtil.getMimeTypeByExtension("txt"));
        assertEquals("application/pdf", FileInfoUtil.getMimeTypeByExtension("pdf"));
        assertEquals("image/jpeg", FileInfoUtil.getMimeTypeByExtension("jpg"));
        assertEquals("image/png", FileInfoUtil.getMimeTypeByExtension("png"));
        assertEquals("application/msword", FileInfoUtil.getMimeTypeByExtension("doc"));
        
        // 测试大写扩展名
        assertEquals("application/octet-stream", FileInfoUtil.getMimeTypeByExtension("TXT"));
        
        // 测试未知扩展名
        assertEquals("application/octet-stream", FileInfoUtil.getMimeTypeByExtension("unknown"));
        
        // 测试空值和null
        assertEquals("application/octet-stream", FileInfoUtil.getMimeTypeByExtension(""));
        assertEquals("application/octet-stream", FileInfoUtil.getMimeTypeByExtension(null));
    }

    @Test
    public void testIsImageByExtension() {
        // 测试图片类型
        assertTrue(FileInfoUtil.isImageByExtension("jpg"));
        assertTrue(FileInfoUtil.isImageByExtension("jpeg"));
        assertTrue(FileInfoUtil.isImageByExtension("png"));
        assertTrue(FileInfoUtil.isImageByExtension("gif"));
        assertTrue(FileInfoUtil.isImageByExtension("webp"));
        
        // 测试非图片类型
        assertFalse(FileInfoUtil.isImageByExtension("txt"));
        assertFalse(FileInfoUtil.isImageByExtension("pdf"));
        assertFalse(FileInfoUtil.isImageByExtension("doc"));
        
        // 测试空值和null
        assertFalse(FileInfoUtil.isImageByExtension(""));
        assertFalse(FileInfoUtil.isImageByExtension(null));
    }

    @Test
    public void testIsExtensionMatchingMimeType() {
        // 测试匹配的情况
        assertTrue(FileInfoUtil.isExtensionMatchingMimeType("txt", "text/plain"));
        assertTrue(FileInfoUtil.isExtensionMatchingMimeType("jpg", "image/jpeg"));
        assertTrue(FileInfoUtil.isExtensionMatchingMimeType("pdf", "application/pdf"));
        
        // 测试不匹配的情况
        assertFalse(FileInfoUtil.isExtensionMatchingMimeType("txt", "image/jpeg"));
        assertFalse(FileInfoUtil.isExtensionMatchingMimeType("jpg", "text/plain"));
        
        // 测试空值和null
        assertFalse(FileInfoUtil.isExtensionMatchingMimeType("", "text/plain"));
        assertFalse(FileInfoUtil.isExtensionMatchingMimeType(null, "text/plain"));
        assertFalse(FileInfoUtil.isExtensionMatchingMimeType("txt", ""));
        assertFalse(FileInfoUtil.isExtensionMatchingMimeType("txt", null));
    }

    @Test
    public void testIsSupportImageByExtension() {
        // 测试支持的图片类型
        assertTrue(FileInfoUtil.isSupportImageByExtension("jpg"));
        assertTrue(FileInfoUtil.isSupportImageByExtension("jpeg"));
        assertTrue(FileInfoUtil.isSupportImageByExtension("png"));
        assertTrue(FileInfoUtil.isSupportImageByExtension("gif"));
        assertTrue(FileInfoUtil.isSupportImageByExtension("bmp"));
        assertTrue(FileInfoUtil.isSupportImageByExtension("webp"));
        
        // 测试不支持的图片类型
        assertFalse(FileInfoUtil.isSupportImageByExtension("tiff"));
        assertFalse(FileInfoUtil.isSupportImageByExtension("svg"));
        
        // 测试非图片类型
        assertFalse(FileInfoUtil.isSupportImageByExtension("txt"));
        assertFalse(FileInfoUtil.isSupportImageByExtension("pdf"));
        
        // 测试空值和null
        assertFalse(FileInfoUtil.isSupportImageByExtension(""));
        assertFalse(FileInfoUtil.isSupportImageByExtension(null));
    }

    @Test
    public void testIsSupportImageByMimeType() {
        // 测试支持的图片MIME类型
        assertTrue(FileInfoUtil.isSupportImageByMimeType("image/jpeg"));
        assertTrue(FileInfoUtil.isSupportImageByMimeType("image/png"));
        assertTrue(FileInfoUtil.isSupportImageByMimeType("image/gif"));
        assertTrue(FileInfoUtil.isSupportImageByMimeType("image/bmp"));
        assertTrue(FileInfoUtil.isSupportImageByMimeType("image/webp"));
        
        // 测试不支持的图片MIME类型
        assertFalse(FileInfoUtil.isSupportImageByMimeType("image/tiff"));
        assertFalse(FileInfoUtil.isSupportImageByMimeType("image/svg+xml"));
        
        // 测试非图片MIME类型
        assertFalse(FileInfoUtil.isSupportImageByMimeType("text/plain"));
        assertFalse(FileInfoUtil.isSupportImageByMimeType("application/pdf"));
        
        // 测试空值和null
        assertFalse(FileInfoUtil.isSupportImageByMimeType(""));
        assertFalse(FileInfoUtil.isSupportImageByMimeType(null));
    }

    @Test
    public void testGetWH() {
        // 测试有效的尺寸字符串
        assertEquals(Pair.build(800, 600), FileInfoUtil.getWH("800*600"));
        assertEquals(Pair.build(1920, 1080), FileInfoUtil.getWH("1920*1080"));
        assertEquals(Pair.build(100, 100), FileInfoUtil.getWH("100*100"));
        
        // 测试超出限制的尺寸
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH("6000*4000"));
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH("5001*2000"));
        
        // 测试无效的格式
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH("800x600"));
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH("800"));
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH("axb"));
        
        // 测试空值和null
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH(""));
        assertEquals(Pair.build(0, 0), FileInfoUtil.getWH(null));
    }
} 