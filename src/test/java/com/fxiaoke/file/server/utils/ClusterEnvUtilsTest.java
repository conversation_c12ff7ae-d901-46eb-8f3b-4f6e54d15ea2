package com.fxiaoke.file.server.utils;

import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.k8s.support.util.SystemUtils.RuntimeEnv;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

import static org.junit.jupiter.api.Assertions.*;

class ClusterEnvUtilsTest {

    @BeforeEach
    void setUp() {
        resetEnvironmentState();
    }

    @ParameterizedTest
    @EnumSource(
        value = RuntimeEnv.class,
        names = {"FIRSTSHARE", "FONESHARE", "PRIVATE_DEPLOY_CLOUD"}
    )
    void testInitWithFacishareOrPrivateCluster(RuntimeEnv env) {
        // 初始化特定环境
        ClusterEnvUtils.init(env);
        
        // 验证结果
        assertTrue(ClusterEnvUtils.isFacishareOrPrivateCluster(), 
            "应该识别为Facishare或私有集群环境: " + env.name());
        assertTrue(ClusterEnvUtils.isExistsAvatarFileSystem(), 
            "应该存在头像文件系统: " + env.name());
    }

    @Test
    void testInitWithNonFacishareEnvironment() {
        // 测试其他环境
        RuntimeEnv[] otherEnvs = {
            RuntimeEnv.DEDICATED_CLOUD
        };

        for (RuntimeEnv env : otherEnvs) {
            // 重置状态
            resetEnvironmentState();
            // 初始化环境
            ClusterEnvUtils.init(env);
            
            // 验证结果
            assertFalse(ClusterEnvUtils.isFacishareOrPrivateCluster(), 
                "不应该识别为Facishare或私有集群环境: " + env.name());
            assertFalse(ClusterEnvUtils.isExistsAvatarFileSystem(), 
                "不应该存在头像文件系统: " + env.name());
        }
    }

    @Test
    void testStaticInitialization() {
        // 获取当前实际运行环境
        RuntimeEnv currentEnv = SystemUtils.getRuntimeEnv();
        
        // 重新初始化以模拟静态初始化
        ClusterEnvUtils.init(currentEnv);
        
        // 验证初始化结果与当前环境一致
        if (currentEnv == RuntimeEnv.FIRSTSHARE || 
            currentEnv == RuntimeEnv.FONESHARE || 
            currentEnv == RuntimeEnv.PRIVATE_DEPLOY_CLOUD) {
            
            assertTrue(ClusterEnvUtils.isFacishareOrPrivateCluster(),
                String.format("当前环境[%s]应该识别为Facishare或私有集群环境", currentEnv.name()));
            assertTrue(ClusterEnvUtils.isExistsAvatarFileSystem(),
                String.format("当前环境[%s]应该存在头像文件系统", currentEnv.name()));
        } else {
            assertFalse(ClusterEnvUtils.isFacishareOrPrivateCluster(),
                String.format("当前环境[%s]不应该识别为Facishare或私有集群环境", currentEnv.name()));
            assertFalse(ClusterEnvUtils.isExistsAvatarFileSystem(),
                String.format("当前环境[%s]不应该存在头像文件系统", currentEnv.name()));
        }
    }

    @Test
    void testPrivateConstructor() {
        Constructor<ClusterEnvUtils> constructor = null;
        try {
            constructor = ClusterEnvUtils.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            constructor.newInstance();
            fail("应该抛出异常，因为构造函数是私有的");
        } catch (InvocationTargetException e) {
            // 期望抛出异常
            assertTrue(e.getTargetException() instanceof IllegalStateException,
                "应该抛出IllegalStateException");
        } catch (Exception e) {
            fail("出现意外异常: " + e.getMessage());
        } finally {
            if (constructor != null) {
                constructor.setAccessible(false);
            }
        }
    }
    
    /**
     * 重置环境状态的辅助方法
     * 通过反射重置静态字段
     */
    private void resetEnvironmentState() {
        try {
            var facishareField = ClusterEnvUtils.class.getDeclaredField("facishareOrPrivateCluster");
            var avatarField = ClusterEnvUtils.class.getDeclaredField("existsAvatarFileSystem");
            
            facishareField.setAccessible(true);
            avatarField.setAccessible(true);
            
            facishareField.set(null, false);
            avatarField.set(null, false);
            
            facishareField.setAccessible(false);
            avatarField.setAccessible(false);
        } catch (Exception e) {
            fail("无法重置环境状态: " + e.getMessage());
        }
    }
} 